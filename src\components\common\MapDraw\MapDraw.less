.spinWrap {
  width: 100%;
  height: 100%;

  .ant-spin-container {
    width: 100%;
    height: 100%;
  }
}

.cesium-viewer-fullscreenContainer {
  display: none !important;
}

.ant-card-body {
  padding-top: 0;
}

.map-container {
  height: 100%;
  width: 100%;
  position: relative;

  .toolbar {
    display: flex;
    justify-content: flex-start;
    padding: 10px;
    flex-wrap: wrap;
    z-index: 1;
    position: absolute;
    align-content: flex-start;
    pointer-events: none;
    top: 0;
    left: 0;
    right: 0;

    .inputbar{
      margin-top: 12px;
      margin-left: 32px;
      z-index: 1;
      pointer-events: all;
    }

    .result-panel {
      width: 320px;
      max-height: 350px;
      display: flex;
      flex-direction: column;
      margin-bottom: 10px;
      pointer-events: all;

      &.ant-card {
        background-color: rgba(255, 255, 255, 0.1);

        .ant-select-selector {
          background-color: rgba(255, 255, 255, 0.7);
        }

        .ant-card-head {
          padding: 0 12px;
        }

        .ant-card-head-title {
          padding: 12px 0;
        }
      }

      .search-bar {
        display: flex;
        align-items: center;

        .text {
          text-overflow: ellipsis;
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
        }
      }

      .result-list.ant-list-loading {
        min-height: 100px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .info {
      padding: 12px 12px 0 12px;
      background-color: rgba(255, 255, 255, 0);
      border-radius: 2px;
      border: 0;
      pointer-events: all;

      .item {
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        .input-bar{

        }
        .search-bar {
          justify-content: space-around;
          margin-left: 20px;
          display: flex;
          flex-direction: column;
          align-items: center;

          .search-bar-input {
            display: flex;
            justify-content: space-between;

            .inputArea {
              min-width: 250px;
            }

            .map-search-btn {
              margin-left: 5px;
            }

            .map-clear-btn {
              margin-left: 5px;
            }

          }

          .search-bar-result {
            position: absolute;
            width: 30%;
            top: 40px;
            left: 30px;
            background-color: rgba(0, 0, 0, 0.6);
            margin-bottom: 10px;
            padding-left: 10px;
            text-align: left;
            color: #FFF;
          }
        }

        .edit:hover {
          cursor: pointer;
        }

        .color {
          width: 20px;
          height: 20px;
          border-radius: 50%;
        }
      }

      .ant-input {
        background-color: rgba(255, 255, 255, 0.9);
        height: 40px;
        border-radius: 5px;
      }

      .addressText {
        margin-bottom: 0;
        color: rgba(0, 0, 0, 1);
        max-width: 210px;
      }

      input[type="number"] {
        -moz-appearance: textfield;
        appearance: textfield;
      }

      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        appearance: none;
        margin: 0;
      }
    }
  }

  .colorArea {
    z-index: 1;
    left: 20%;
    bottom: 20px;
    position: absolute;
    display: flex;
    align-items: center;

    .reset-button {
      margin-left: 10px;
      height: 100%;
    }
  }

  .bottonArea {
    height: 200px;
    width: 60px;
    z-index: 1;
    bottom: 0;
    right: 20px;
    position: absolute;

    .bottonUse {
      width: 60px;
      height: 60px;
      border-radius: 60px;
      text-align: center;
      line-height: 60px;
      margin-top: 20px;
    }

    .bottonUse:hover {
      cursor: pointer;
    }

    .cancelbotton {
      background-color: rgba(221, 197, 41, 1);
    }

    .confirmbotton {
      background-color: rgba(22, 155, 213, 1);
      color: aliceblue;
    }

  }
}

.map-popup-container {
  width: 60vw;
  height: 60vh;
}
