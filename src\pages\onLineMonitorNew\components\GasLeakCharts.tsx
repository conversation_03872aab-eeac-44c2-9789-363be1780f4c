import React, { Key, useEffect, useMemo, useRef, useState } from 'react';
import * as echarts from 'echarts/core';
import {
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LegendComponent,
} from 'echarts/components';
import { LineChart, BarChart } from 'echarts/charts';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import { Form, Button, Row, Col, DatePicker, Select, message, Spin } from 'antd';
import moment from 'moment';
import { isNumber } from 'yth-ui/es/components/util/util';
import formApi from '@/service/formApi';
import type { ResponseType, ResponsPageType } from '@/service/envApi';
import { ECBasicOption } from 'echarts/types/dist/shared';
import {
  queryEquipInfoByCompanyId,
  queryMonitorIndex,
  queryMonitorIndexValueItem,
} from '@/service/envApi';
import { monitorTypeConfigs } from './constants';
import { DateListTypes, MonitorTypeConfig } from './types';
import style from './index.module.less';

const { RangePicker } = DatePicker;

echarts.use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LineChart,
  BarChart,
  CanvasRenderer,
  LegendComponent,
  UniversalTransition,
]);

type objType = Record<string, string>;

type LegendSelectChangedEvent = {
  name: string;
  type: 'legendselectchanged';
  selected: Record<string, boolean>;
};

type PropsType = {
  /** 图表类型-均值，累计值 */
  chartType: 'avg' | 'sum';
  /** 监测类型 */
  monitorKey: string;
  /** 父级传入的查询参数 */
  queryData: Record<string, string>;
  /** 在线监测类型 */
  onLineMonitorType: string;
  /** 所属单位数据 */
  companyList: Record<string, string>[];
  /** 采集频率字典数据 */
  monitorFrequencyList: DateListTypes[];
  /** 监测类型配置-环境质量监测，污染源监测，公用工程监测 */
  monitorConfigType: string;
};

/**
 * @description echarts 显示 折线图或柱状图
 * @param props
 * @returns
 */
const GasLeakCharts: React.FC<PropsType> = (props) => {
  const {
    chartType,
    monitorKey,
    onLineMonitorType,
    queryData,
    companyList,
    monitorConfigType,
    monitorFrequencyList,
  } = props;
  const config: MonitorTypeConfig = monitorTypeConfigs[monitorConfigType];
  const chartRef: React.MutableRefObject<HTMLDivElement | null> = useRef(null);
  const chartInstance: React.MutableRefObject<echarts.ECharts | null> = useRef(null);
  const [magictype, setMagictype] = useState<'bar' | 'line'>('line');
  const [form] = Form.useForm();
  const [selected, setSelected] = useState<Record<string, boolean>>({});
  const [columnList, setColumnList] = useState<Array<Record<string, React.Key>>>([]); // 表头数据
  const [loading, setLoading] = useState<boolean>(false);
  const [deviceList, setDeviceList] = useState<objType[]>([]); // 设备数据
  const [monitorList, setMonitorList] = useState<objType[]>([]); // 监测类别
  const [listDatas, setListData] = useState<objType[]>([]); // 查询table 接口返回数据
  const [dateTypeKey, setDateTypeKey] = useState<string>(''); // 时间类型-默认实时数据
  const [selectedEquipId, setSelectedEquipId] = useState<string>(''); // 当前选中的设备ID
  const [onlyOne, setOnlyOne] = useState<null | number>(null); // 判断是否图例只剩下一个
  const [overWeek, setOverWeek] = useState<number>(0); // 时间范围限制

  // 获取监测类型数据
  const getMonitorList: () => Promise<void> = async () => {
    // 获取监测类型字典数据
    const { list } = await formApi.getDictionary({
      condition: {
        // 根据配置决定使用哪个父级字典码
        fatherCode: config.filterMonitorList ? 'A22A08A07' : monitorKey,
      },
      currentPage: 0,
      pageSize: 0,
    });

    let finalList: objType[] = list;
    // 如果需要过滤监测类型列表
    if (config.filterMonitorList && config.filterCondition) {
      finalList = list?.filter(config.filterCondition) || [];
    }
    setMonitorList([...finalList]);
  };

  /**
   * @description 修改时间范围
   * @param {moment.Moment} rangeDate - 选中的时间范围
   * @param {string} type - 时间类型
   */
  type ChangeRangeType = (
    rangeDate: moment.Moment[] | moment.Moment,
    key: string,
    startTime?: boolean,
  ) => string;
  const changeRange: ChangeRangeType = (rangeDate, key, startTime = true) => {
    const dataCodeKey: string | undefined = monitorFrequencyList.find(
      (item) => item.key === key,
    )?.dataCode;

    // 年均数据 (dataCode: '9999') 只有一个开始时间
    if (dataCodeKey === '9999') {
      const startDate: string = moment(rangeDate as moment.Moment)
        .startOf('year')
        .format('YYYY-MM-DD HH:mm:ss');
      const endDate: string = moment(rangeDate as moment.Moment)
        .endOf('year')
        .format('YYYY-MM-DD HH:mm:ss');
      return startTime ? startDate : endDate;
    }
    // 日均数据 (dataCode: '2031') 需要日期的开始结束时间
    if (dataCodeKey === '2031') {
      const startDate: string = Array.isArray(rangeDate)
        ? rangeDate[0].startOf('day').format('YYYY-MM-DD HH:mm:ss')
        : '';
      const endDate: string = Array.isArray(rangeDate)
        ? rangeDate[1].endOf('day').format('YYYY-MM-DD HH:mm:ss')
        : '';
      return startTime ? startDate : endDate;
    }
    return startTime
      ? Array.isArray(rangeDate) && rangeDate[0].format('YYYY-MM-DD HH:mm:ss')
      : Array.isArray(rangeDate) && rangeDate[1].format('YYYY-MM-DD HH:mm:ss');
  };

  /**
   * @description 获取tableList数据
   */
  const queryListData: (id?: string) => Promise<void> = async (id?: string) => {
    const { rangeDate, formCompany, deviceCode, formDateType } = form.getFieldsValue();
    const equipId: string = id ?? deviceList.find((str) => str.code === deviceCode)?.id;
    if (overWeek) {
      message.info(`监测时间不能超过${overWeek}天!!!`);
      return;
    }
    setLoading(true);
    const resData: ResponseType = await queryMonitorIndexValueItem({
      monitorType: onLineMonitorType,
      equipCd: deviceCode,
      reportType: '2',
      equipId,
      orgCd: formCompany || '',
      startTm: changeRange(rangeDate, formDateType, true),
      endTm: changeRange(rangeDate, formDateType, false),
      dateType: formDateType,
      sort: 'ASC',
    });
    if (resData.code && resData.code === 200 && Array.isArray(resData.data)) {
      const selectedList: Record<string, boolean> = {};
      resData.data?.forEach((str, index) => {
        selectedList[str.name] = index === 0;
      });
      setListData([...resData.data]);
      setSelected(selectedList);
      if (columnList.length !== 1) {
        setOnlyOne(null);
      }
    } else {
      message.error('请求数据出错，请刷新重试或联系管理员');
      setListData([]);
    }
    setLoading(false);
  };

  /**
   * @description 获取表头列数据并更改结构
   * 使用当前选中的设备ID和监测频率获取监测指标
   */
  const getColData: () => Promise<void> = async () => {
    // 检查必要参数：设备ID和监测频率都必须存在
    if (!selectedEquipId || !dateTypeKey) {
      // 缺少必要参数时，清空数据
      setColumnList([]);
      return;
    }

    setSelected({});
    setLoading(true);
    const data: ResponsPageType = await queryMonitorIndex(selectedEquipId, dateTypeKey);
    if (data.code === 200 && data.data.length > 0) {
      const newList: objType[] = (data.data as objType[]) || [];
      setColumnList([...newList]);
      if (newList.length === 1) {
        setOnlyOne(1);
      }
      queryListData(selectedEquipId);
    } else {
      setColumnList([]);
    }
    setLoading(false);
  };

  /**
   * @description 根据选择公司获取设备
   */
  const getDeviceData: (reset?: boolean) => Promise<void> = async (reset = true) => {
    const { monitorType, formCompany } = form.getFieldsValue();
    setListData([]);
    setColumnList([]);
    setLoading(true);
    const data: ResponseType = await queryEquipInfoByCompanyId({
      companyId: formCompany,
      type: monitorKey,
      monitorType,
      monitorOnline: '1',
    });
    if (data.code === 200 && Array.isArray(data.data) && data.data.length > 0) {
      // 如果reset为true，则设置默认设备
      if (reset) {
        form.setFieldsValue({ deviceCode: data.data[0]?.code });
        // 设置选中的设备ID（这会触发useEffect自动调用getColData）
        setSelectedEquipId(data.data[0]?.id || '');
      }
      setDeviceList([...data.data]);
    } else {
      form.resetFields(['deviceCode']);
      setDeviceList([]);
      setSelectedEquipId('');
    }
    setLoading(false);
  };

  const formatterNumber: (value: number) => number | string = (value: number) => {
    return value ? Math.trunc(value * 1000) / 1000 : '-';
  };

  // 遍历数组并计数true值的出现
  const onlyOneTrue: (arr: boolean[]) => number | null = (arr: boolean[]): number | null => {
    const trueIndices: number[] = arr
      .map((value, index) => (value ? index : -1))
      .filter((i) => i !== -1);
    return trueIndices.length === 1 ? trueIndices[0] : null;
  };

  const option: ECBasicOption = useMemo(() => {
    const seriesList: object[] = [];
    columnList?.forEach((column) => {
      seriesList.push({
        name: column.name,
        type: magictype,
        data: listDatas.map((item) => item[column.code + chartType]),
        symbol: 'circle',
        symbolSize: 1,
        smooth: true,
      });
    });
    return {
      tooltip: {
        trigger: 'axis',
        formatter(params: Record<string, string | number>[]) {
          let result: string = `${params[0].name}<br>`; // 获取横轴对应的数据作为提示信息的标题
          // 先添加常规数据
          params.forEach((item: Record<string, string | number>) => {
            result += `${item.marker} ${item.seriesName}: ${formatterNumber(item.value as number)}`; // 对折线图数据进行格式化
            result += columnList[item.seriesIndex as number]?.measureUnit || '';
            result += `</div><div>`;
          });

          // 如果只选中一个指标，添加上下限值
          if (isNumber(onlyOne)) {
            const upperLimit: Key = columnList[onlyOne]?.firstLevelMax;
            const lowerLimit: Key = columnList[onlyOne]?.firstLevelMin;
            result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#FF4500;"></span> 上限值: ${formatterNumber(upperLimit as number)}</div><div>`;
            result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#1E90FF;"></span> 下限值: ${formatterNumber(lowerLimit as number)}`;
          }

          return result;
        },
      },
      legend: {
        right: '6%',
        top: '-1%',
        data: columnList.map((str) => str.name),
        selected, // 默认不需要显示的设置为false
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      toolbox: {
        feature: {
          magicType: {
            type: ['line', 'bar'],
            title: {
              line: '切换为折线图',
              bar: '切换为柱状图',
            },
          },
          saveAsImage: {
            // 保存为图片
            show: true, // 是否显示该工具
            title: '保存',
          },
        },
        top: '3%',
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: listDatas?.map((item) => item.TIME) || [],
      },
      yAxis: {
        type: 'value',
      },
      dataZoom: {
        startValue: 0,
        // 数据窗口范围的结束数值（一页显示多少条数据）
        endValue: 15,
      },
      series: seriesList,
    };
  }, [chartType, selected, columnList, listDatas, onlyOne]);

  // 只在组件挂载时初始化一次图表
  useEffect(() => {
    if (chartRef.current) {
      // 初始化图表
      chartInstance.current = echarts.init(chartRef.current);

      // 设置事件监听
      chartInstance.current.on('legendselectchanged', (event: LegendSelectChangedEvent) => {
        const listData: boolean[] = Object.values(event.selected);
        setOnlyOne(onlyOneTrue(listData));
        setSelected(event.selected);
      });

      chartInstance.current.on('magictypechanged', (event: { currentType: 'bar' | 'line' }) => {
        setMagictype(event.currentType);
      });
    }
    // 组件卸载时销毁图表
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, []); // 空依赖数组，只执行一次

  /**
   * @description 检查时间范围是否超限
   * @param {[moment.Moment, moment.Moment]} dateRange - 时间范围
   */
  const checkDateRangeLimit: (dateRange: moment.Moment[]) => void = (dateRange) => {
    if (!dateRange || !dateRange[0] || !dateRange[1]) return;

    const dateConfig: DateListTypes | undefined = monitorFrequencyList.find(
      (item) => item.key === dateTypeKey,
    );
    const overDay: number = dateConfig?.over;
    if (dateRange[1].diff(dateRange[0], 'days') > overDay) {
      message.warning(`监测时间范围请勿超过${overDay}天`);
      setOverWeek(overDay);
    } else {
      setOverWeek(0);
    }
  };

  /**
   * @description 渲染不同类型的日期选择器
   * @returns {React.ReactNode} 日期选择器组件
   */
  const renderDatePicker: () => React.ReactNode = () => {
    const dateConfig: DateListTypes | undefined = monitorFrequencyList.find(
      (item) => item.key === dateTypeKey,
    );
    if (!dateConfig) return null;

    // 获取表单中的日期范围值
    const dateRange: moment.Moment[] | moment.Moment = form.getFieldValue('rangeDate');
    switch (dateConfig.pickerType) {
      case 'year':
        // 年份选择器
        return (
          <DatePicker
            picker="year"
            format={dateConfig.format}
            value={dateRange as moment.Moment}
            size="large"
            onChange={(date) => {
              if (date) {
                form.setFieldsValue({ rangeDate: date });
              }
            }}
            disabledDate={(current) => {
              // 禁用未来年份
              return current && current > moment().endOf('year');
            }}
          />
        );
      case 'date':
        // 日期选择器（不带时间）
        return (
          <RangePicker
            picker="date"
            size="large"
            format={dateConfig.format}
            value={dateRange as [moment.Moment, moment.Moment]}
            onChange={(dates) => {
              form.setFieldsValue({ rangeDate: dates });
              if (dates) {
                checkDateRangeLimit(dates);
              }
            }}
          />
        );
      case 'dateTime':
      default:
        // 日期时间选择器
        return (
          <RangePicker
            showTime
            size="large"
            format={dateConfig.format}
            value={dateRange as [moment.Moment, moment.Moment]}
            onOk={(dates) => {
              if (dates) {
                checkDateRangeLimit(dates);
              }
            }}
          />
        );
    }
  };

  // 单独的 effect 用于更新图表配置
  useEffect(() => {
    if (chartInstance.current) {
      // 根据新数据更新图表配置
      chartInstance.current.setOption(option, true);
    }
  }, [chartType, columnList, listDatas, onlyOne, selected]); // 只在这些值变化时更新配置

  useEffect(() => {
    if (monitorKey !== 'A22A08A01') {
      getMonitorList();
    }
  }, [monitorKey]);

  // 监听设备ID和采集频率变化，重新获取列数据
  useEffect(() => {
    if (selectedEquipId && dateTypeKey) {
      getColData();
    }
  }, [selectedEquipId, dateTypeKey]);

  useEffect(() => {
    // 根据父级传入的表单数据 设置表单数据
    form.setFieldsValue({
      formCompany: queryData?.formCompany,
      formDateType: queryData?.formDateType,
      deviceCode: queryData?.deviceCode,
      monitorType: queryData?.monitorType,
      rangeDate: queryData?.rangeDate,
    });
    setDateTypeKey(queryData?.formDateType);
    // 设置选中的设备ID
    if (queryData?.selectedEquipId) {
      setSelectedEquipId(queryData.selectedEquipId);
    }
    getDeviceData(false);
  }, [queryData]);

  return (
    <div>
      <Form
        initialValues={{
          rangeDate: [moment().startOf('day'), moment()],
          formDateType: '2011', // 实时数据的dataCode
        }}
        form={form}
        name="GasLeakListNew"
        className={style['ant-gasleak-form']}
        onFinish={() => {
          queryListData();
        }}
      >
        <Row gutter={16} style={{ height: 50 }}>
          <Col span={6}>
            <Form.Item className={style['form-picker']} name="formCompany" label="所属单位">
              <Select
                placeholder="请选择"
                showSearch
                style={{ fontSize: 12 }}
                onSearch={() => {
                  getDeviceData();
                }}
                optionFilterProp="children"
                filterOption={(input: string, options?: { children: string; value: string }) =>
                  (options?.children ?? '').toLowerCase().includes(input.toLowerCase())
                }
                onChange={() => {
                  getDeviceData();
                }}
              >
                {(companyList || []).map((item) => (
                  <Select.Option key={item.companyId}>{item.supplyUnit || '-'}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          {monitorKey && monitorKey !== 'A22A08A01' && (
            <Col span={6}>
              <Form.Item name="monitorType" label="监测类型">
                <Select
                  placeholder="请选择"
                  showSearch
                  style={{ fontSize: 12 }}
                  onChange={() => {
                    setSelectedEquipId('');
                    form.resetFields(['deviceCode']);
                    getDeviceData();
                  }}
                >
                  {(monitorList || []).map((item) => (
                    <Select.Option key={item.code}>{item.text}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          )}
          <Col span={6}>
            <Form.Item name="deviceCode" label="设备名称">
              <Select
                placeholder="请选择"
                showSearch
                style={{ fontSize: 12 }}
                optionFilterProp="children"
                filterOption={(input: string, options?: { children: string; value: string }) =>
                  (options?.children ?? '').toLowerCase().includes(input.toLowerCase())
                }
                onChange={(deviceCode) => {
                  // 更新选中的设备ID
                  const equipId: string =
                    deviceList.find((item) => item.code === deviceCode)?.id || '';
                  setSelectedEquipId(equipId);
                }}
              >
                {deviceList.map((item) => (
                  <Select.Option key={item.code}>{item.name}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="formDateType" label="采集频率">
              <Select
                placeholder="请选择"
                style={{ fontSize: 12 }}
                onChange={(e) => {
                  // 重置时间范围限制
                  setOverWeek(0);
                  // 设置时间类型
                  setDateTypeKey(e);
                  // 获取对应时间类型的配置
                  const dateConfig: DateListTypes | undefined = monitorFrequencyList.find(
                    (item) => item.key === e,
                  );
                  if (dateConfig) {
                    // 设置默认时间范围
                    form.setFieldsValue({
                      rangeDate: dateConfig.defaultRange ? dateConfig.defaultRange() : [],
                    });
                  }
                }}
              >
                {monitorFrequencyList.map((item) => (
                  <Select.Option key={item.key}>{item.text}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16} style={{ height: 50 }}>
          <Col span={9}>
            <Form.Item name="rangeDate" label="监测时间">
              {renderDatePicker()}
            </Form.Item>
          </Col>
          <Col span={12} style={{ textAlign: 'end' }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
              <Button
                style={{ padding: '0 25px', margin: '0 8px', fontSize: 12 }}
                type="primary"
                htmlType="submit"
              >
                查询
              </Button>
              <Button
                style={{ padding: '0 25px', margin: '0 8px', fontSize: 12 }}
                onClick={() => {
                  form.resetFields();
                  setDeviceList([]);
                  // 重置表单 数据还原到初始化状态
                  form.setFieldsValue({
                    formCompany: queryData?.formCompany,
                    formDateType: queryData?.formDateType,
                    deviceCode: queryData?.deviceCode,
                    monitorType: queryData?.monitorType,
                    rangeDate: queryData?.rangeDate,
                  });
                  getDeviceData();
                }}
              >
                重置
              </Button>
            </div>
          </Col>
        </Row>
      </Form>
      <Spin spinning={loading}>
        <div ref={chartRef} style={{ width: '100%', height: 500 }} />
      </Spin>
    </div>
  );
};
export default GasLeakCharts;
