import React, { useState, useEffect, useRef } from 'react';
import { Modal, DatePicker, Space, Button, Spin, message } from 'antd';
import moment from 'moment';
import analysisApi from '@/service/analysisApi';
import * as echarts from 'echarts/core';
import {
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
} from 'echarts/components';
import { LineChart } from 'echarts/charts';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import type { ApiResponse, MonitorIndexChart, MonitorIndexChartInfo } from '@/service/analysisApi';
import { ECBasicOption } from 'echarts/types/dist/shared';

import style from '../index.module.less';

// 注册 ECharts 组件
echarts.use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  Line<PERSON>hart,
  CanvasRenderer,
  UniversalTransition,
]);

/**
 * @description 图表数据类型
 */
type ChartDataType = {
  time: string;
  value: number | null;
};

/**
 * @description API返回的数据类型
 */
type ApiDataType = {
  monitorTime: string;
  code: string;
  name: string;
  measureUnit: string;
  firstLevelMax: number | null;
  firstLevelMin: number | null;
  secondLevelMax: number | null;
  secondLevelMin: number | null;
  isTotalizeValue: boolean | null;
  number: number;
};

/**
 * @description 组件属性类型
 */
type PropsType = {
  /** 是否显示弹窗 */
  visible: boolean;
  /** 关闭弹窗回调 */
  onClose: () => void;
  /** 设备ID */
  equipId: string;
  /** 监测指标信息 */
  monitorInfo: {
    code: string;
    name: string;
    measureUnit: string;
  };
};

/**
 * @description 监测数据折线图组件
 * @param props 组件属性
 * @returns React组件
 */
const MonitorDataChart: React.FC<PropsType> = (props) => {
  const { visible, onClose, equipId, monitorInfo } = props;

  // 状态管理
  const [chartData, setChartData] = useState<ChartDataType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 默认时间：今天开始到现在
  const [startTime, setStartTime] = useState<moment.Moment>(moment().startOf('day'));
  const [endTime, setEndTime] = useState<moment.Moment>(moment());

  // 图表相关
  const chartRef: React.MutableRefObject<HTMLDivElement | null> = useRef(null);
  const chartInstance: React.MutableRefObject<echarts.ECharts | null> = useRef(null);

  // 验证时间范围是否超过7天
  const validateTimeRange: () => boolean = () => {
    const duration: number = endTime.diff(startTime, 'days');
    if (duration > 7) {
      message.warning('查询时间范围不能超过7天，请重新选择时间范围');
      return false;
    }
    if (startTime.isAfter(endTime)) {
      message.warning('开始时间不能晚于结束时间');
      return false;
    }
    return true;
  };

  // 查询数据
  const queryChartData: () => Promise<void> = async () => {
    // 检查必要参数
    if (!equipId || !monitorInfo.code) {
      return;
    }

    // 验证时间范围
    if (!validateTimeRange()) {
      return;
    }

    try {
      setLoading(true);
      // 构建API请求参数
      const params: MonitorIndexChart = {
        equipId,
        indexCd: monitorInfo.code,
        startTm: startTime.format('YYYY-MM-DD HH:mm:ss'),
        endTm: endTime.format('YYYY-MM-DD HH:mm:ss'),
      };

      // 调用真实API
      const response: ApiResponse<MonitorIndexChartInfo> =
        await analysisApi.queryMonitorIndexChartInfo(params);
      // 处理API返回的数据
      if (response.code === 200 && Array.isArray(response.data)) {
        const processedData: ChartDataType[] = response.data.map((item: ApiDataType) => ({
          time: moment(item.monitorTime).format('YYYY-MM-DD HH:mm:ss'),
          value: typeof item.number === 'number' && !Number.isNaN(item.number) ? item.number : null,
        }));
        setChartData(processedData);
      } else {
        setChartData([]);
      }
    } catch {
      setChartData([]);
    } finally {
      setLoading(false);
    }
  };

  // 重置时间为今天开始到现在
  const resetTimeRange: () => void = () => {
    setStartTime(moment().startOf('day'));
    setEndTime(moment());
  };

  // 禁用开始时间的日期
  const disabledStartDate: (current: moment.Moment) => boolean = (current: moment.Moment) => {
    if (!current) return false;
    // 如果已选择结束时间，开始时间不能大于结束时间
    if (endTime) {
      return current.isAfter(endTime, 'day');
    }
    return false;
  };

  // 禁用结束时间的日期
  const disabledEndDate: (current: moment.Moment) => boolean = (current: moment.Moment) => {
    if (!current) return false;
    // 如果已选择开始时间，结束时间不能小于开始时间
    if (startTime) {
      return current.isBefore(startTime, 'day');
    }
    return false;
  };

  // 初始化图表
  useEffect(() => {
    if (visible && chartRef.current && !chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
    }
  }, [visible]);

  // 弹窗打开时自动查询数据
  useEffect(() => {
    if (visible) {
      queryChartData();
    }
  }, [visible]);

  // 更新图表数据
  useEffect(() => {
    if (chartInstance.current && chartData.length > 0) {
      const option: ECBasicOption = {
        title: {
          text: `${monitorInfo.name}趋势图`,
          left: 'center',
          textStyle: {
            fontSize: 14,
          },
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params: Record<string, string | number>[]) => {
            const data: Record<string, string | number> = params[0];
            const value: string | number =
              data.value === null || data.value === undefined ? '-' : data.value;
            return `${data.name}<br/>${monitorInfo.name}: ${value} ${monitorInfo.measureUnit}`;
          },
        },
        toolbox: {
          feature: {
            saveAsImage: {
              show: true,
              title: '保存为图片',
            },
          },
          right: '2%',
          top: '0%',
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: chartData.map((item) => item.time),
          axisLabel: {
            rotate: 45,
            fontSize: 12,
          },
        },
        yAxis: {
          type: 'value',
          name: monitorInfo.measureUnit,
          nameTextStyle: {
            fontSize: 12,
          },
          axisLabel: {
            fontSize: 12,
          },
        },
        series: [
          {
            name: monitorInfo.name,
            type: 'line',
            smooth: true,
            data: chartData.map((item) => item.value),
            lineStyle: {
              color: '#1890ff',
              width: 2,
            },
            itemStyle: {
              color: '#1890ff',
            },
            symbol: 'circle',
            symbolSize: 4,
            connectNulls: false, // 不连接空值点
          },
        ],
      };
      chartInstance.current.setOption(option, true);
    }
  }, [chartData, monitorInfo]);

  return (
    <Modal
      title={`${monitorInfo.name}数据趋势图`}
      visible={visible}
      onCancel={onClose}
      footer={null}
      width="70%"
      destroyOnClose
      centered
    >
      {/* 时间选择和查询区域 */}
      <div style={{ marginBottom: 16, fontSize: '12px' }}>
        <Space wrap>
          <span style={{ fontSize: '12px' }}>开始时间：</span>
          <DatePicker
            showTime={{ format: 'HH:mm:ss' }}
            format="YYYY-MM-DD HH:mm:ss"
            value={startTime}
            onChange={(value) => setStartTime(value || moment().startOf('day'))}
            placeholder="选择开始时间"
            className={style['custom-datepicker']}
            disabledDate={disabledStartDate}
          />
          <span style={{ fontSize: '12px' }}>结束时间：</span>
          <DatePicker
            showTime={{ format: 'HH:mm:ss' }}
            format="YYYY-MM-DD HH:mm:ss"
            value={endTime}
            onChange={(value) => setEndTime(value || moment())}
            placeholder="选择结束时间"
            className={style['custom-datepicker']}
            disabledDate={disabledEndDate}
          />
          <Button
            type="primary"
            onClick={queryChartData}
            loading={loading}
            style={{ fontSize: '12px' }}
          >
            查询
          </Button>
          <Button onClick={resetTimeRange} style={{ fontSize: '12px' }}>
            重置
          </Button>
        </Space>
        <div style={{ marginTop: 8, fontSize: '12px', color: '#999' }}>
          提示：查询时间范围最多支持7天
        </div>
      </div>

      {/* 图表显示区域 */}
      <Spin spinning={loading}>
        <div
          ref={chartRef}
          style={{
            width: '100%',
            height: '500px',
            minHeight: '400px',
          }}
        />
      </Spin>
    </Modal>
  );
};

export default MonitorDataChart;
