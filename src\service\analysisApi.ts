import { envRequest } from '@/request';

// ===== 类型定义 =====

/**
 * 通用 API 响应接口
 */
export type ApiResponse<T = unknown> = {
  code: number;
  data: T;
  msg?: string;
  total?: number;
};

/**
 * 时间范围参数接口
 */
interface TimeRangeParams {
  startTm: string;
  endTm: string;
}

/**
 * 设备查询参数接口
 */
interface EquipQueryParams {
  equipId: string;
}

/**
 * 分页查询参数接口
 */
interface PageQueryParams<T = unknown> {
  descs: string[];
  condition: T | Record<string, unknown>;
  currentPage: number;
  pageSize: number;
}

/**
 * 报告生成参数接口
 */
interface ReportGenerateParams {
  reportTime?: string;
  reportName?: string;
  startTm?: string;
  endTm?: string;
  [key: string]: string | number | undefined;
}

/**
 * 监测指标统计图信息查询参数接口
 */
export interface MonitorIndexChart {
  equipId: string;
  indexCd: string;
  startTm: string;
  endTm: string;
}

/**
 * 空气监测信息
 */
export type AirMonitorInfo = {
  airLeavel: number; // 空气质量级别
  airLevelText?: string; // 空气质量级别
  airNum: string; // AQI监测值
  coNum: string; // CO监测值
  complianceDays: number; // 达标天数
  fluoride: string; // O3监测值
  monitorDays: number; // 监测天数
  monitorTime: string; // 监测时间
  no2Num: string; // NO2监测值
  o3Num: string; // O3监测值
  passRate: number; // 合格率
  pm10Num: string; // PM10监测值
  pm25Num: string; // PM2.5监测值
  primaryPollutant: string; // 首要污染物
  so2Num: string; // SO2监测值
};

/**
 * 监测指标统计图信息
 */
export type MonitorIndexChartInfo = {
  code: string; // 指标code
  firstLevelMax: number; // 一级阈值上限
  firstLevelMin: number; // 一级阈值下限
  isTotalizeValue: boolean; // 是否有累计值
  measureUnit: string; // 指标单位
  monitorTime: string; // 监测时间
  name: string; // 指标名称
  number: number; // 指标值
  secondLevelMax: number; // 二级阈值上限
  secondLevelMin: number; // 二级阈值下限
};

/**
 * 分析 API 接口定义
 */
interface AnalysisApiInterface {
  /** 查询空气监测信息 */
  queryAirMonitorInfo: (data: TimeRangeParams) => Promise<ApiResponse<AirMonitorInfo>>;
  /** 根据月份查询监测信息 */
  queryMonitorInfoByMonth: (data: TimeRangeParams) => Promise<ApiResponse<AirMonitorInfo[]>>;
  /** 查询监测指标信息 */
  queryMonitorIndexInfo: (data: EquipQueryParams) => Promise<ApiResponse<MonitorIndexChartInfo>>;
  /** 查询监测指标统计图信息 */
  queryMonitorIndexChartInfo: (
    data: MonitorIndexChart,
  ) => Promise<ApiResponse<MonitorIndexChartInfo>>;
  /** 生成分析报告 */
  generateAnalysisReport: (data: ReportGenerateParams) => Promise<ApiResponse<unknown>>;
  /** 查询分析报告列表 */
  queryAnalysisReportList: (
    data: PageQueryParams<ReportGenerateParams>,
  ) => Promise<ApiResponse<boolean>>;
  /** 删除分析报告 */
  deleteReport: (id: string) => Promise<ApiResponse<boolean>>;
}

/**
 * 数据分析模块 API
 */
const analysisApi: AnalysisApiInterface = {
  /** 查询空气监测信息 */
  queryAirMonitorInfo: (data) => {
    return envRequest.post('/staticAnalysis/queryAirMonitorInfo', { data });
  },
  /** 根据月份查询监测信息 */
  queryMonitorInfoByMonth: (data) => {
    return envRequest.post('/staticAnalysis/queryMonitorInfoByMonth', { data });
  },
  /** 查询监测指标信息 */
  queryMonitorIndexInfo: (data) => {
    return envRequest.post('/staticAnalysis/queryMonitorIndexInfo', { data });
  },
  /** 查询监测指标统计图信息 */
  queryMonitorIndexChartInfo: (data) => {
    return envRequest.post('/staticAnalysis/queryMonitorIndexChartInfo', { data });
  },
  /** 生成分析报告 */
  generateAnalysisReport: (data) => {
    return envRequest.post('/statisticsReport/createAnalysisReports', { data });
  },
  /** 查询分析报告列表 */
  queryAnalysisReportList: (data) => {
    return envRequest.post('/statisticsReport/page', { data });
  },
  /** 删除分析报告 */
  deleteReport: (id) => {
    return envRequest.delete(`/statisticsReport/removeById`, { params: { id } });
  },
};

export default analysisApi;
