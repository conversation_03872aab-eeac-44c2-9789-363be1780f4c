import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Map, TrackingLayer } from 'yth-map';
import { YTHLocalization } from 'yth-ui';
import {
  Row,
  Col,
  message,
  DatePicker,
  Button,
  Modal,
  Table,
  Tooltip,
  Image,
  Select,
  Spin,
} from 'antd';
import moment from 'moment';
import dicParams from '@/utils/dicParams';
import { CurrentUser } from '@/Constant';
import {
  ByParkCodeType,
  getCurrentUser,
  queryByParkCode,
  queryOrganizationTreeListByLoginUser,
} from '@/service/baseModuleApi';
import {
  queryEquipPlugInfo,
  queryAlarmNumByType,
  queryMonitorEquipList,
  queryMonitorRealtimeData,
} from '@/service/envApi';
import { IYTHColumnProps } from 'yth-ui/es/components/list';
import tdt from './img/tdt_img.jpg';
import style from './home.module.less';
import InsertModal from './InsertModal';
import MonotorIndexModal from './MonotorIndexModal';
import AlarmModalView from './AlarmModalView';
import GasLeakCharts from './GasLeakCharts';
import insertNum from './img/insertNum.png';
import onlineNum from './img/onlineNum.png';
import monitorNum from './img/monitorNum.png';
import offlineNum from './img/offlineNum.png';
// import gasMonitor from './img/gasMonitor.png';
import pollution from './img/pollution.png';
import envQuality from './img/envQuality.png';
// import publicWork from './img/publicWork.png';

import pollutionDevice from './img/pollutionDevice.png';
// import gasDevice from './img/gasDevice.png';
// import waterDevice from './img/waterDevice.png';
import envGasQuality from './img/envGasQuality.png';

// const proxy = `${location.origin}/gw/campus-monitor-fron/`
const proxy: string = ``;

const { RangePicker } = DatePicker;

type IconDataTypes = {
  type: string;
  // type: 'A22A08A07A01' | 'A22A08A07A05'|  'A22A08A01' | 'A22A08A06';
  name: string;
  color: string;
  img: string;
};

type alarmDataType = {
  key: string;
  name: string;
  img: string;
};

// 定义设备数据类型
type deviceDataType = {
  alarm: number;
  brand: string;
  code: string;
  companyId: string;
  id: string;
  latitude: number;
  longitude: number;
  monitorOnline: string;
  monitorType: string;
  name: string;
  online: string;
  state: string;
  supplyUnit: string;
  type: string;
};

// 定义整个数据结构的类型
type DeviceLocationMap = Record<IconDataTypes['type'], deviceDataType[]>;

/** 不同监测数据list */
const iconShow: IconDataTypes[] = [
  { type: 'A22A08A07', name: '污染源监测', color: 'cyan', img: pollutionDevice },
  { type: 'A22A08A06', name: '环境空气质量监测', color: 'yellow', img: envGasQuality },

  // { type: 'A22A08A07A01', name: '污染源监测', color: 'cyan', img: pollutionDevice },
  // { type: 'A22A08A07A05', name: '污水处理监测', color: 'orange', img: waterDevice },
  // { type: 'A22A08A01', name: '气体泄漏监测', color: 'yellow', img: gasDevice },
  // { type: 'A22A08A06', name: '环境空气质量监测', color: 'yellow', img: envGasQuality },
];

/** 报警数据list */
const alarmData: alarmDataType[] = [
  { key: 'A22A08A07', name: '污染源监测', img: pollution },
  { key: 'A22A08A06', name: '环境质量监测', img: envQuality },

  // { key: 'A22A08A01', name: '有毒有害气体监测', img: gasMonitor },
  // { key: 'A22A08A03', name: '公用工程监测', img: publicWork },
];

const deviceDataInt: deviceDataType = {
  alarm: 0,
  brand: '',
  code: '',
  companyId: '',
  id: '',
  latitude: 0,
  longitude: 0,
  monitorOnline: '',
  monitorType: '',
  name: '',
  online: '',
  state: '',
  supplyUnit: '',
  type: '',
};

type objType = Record<string, string>;

/**
 * @description 生态环境首页
 * @returns
 */
const Partition: React.FC = () => {
  const oldPartitionList: React.MutableRefObject<Array<{ title: string; key: string }>> = useRef();
  const [companyList, setCompanyList] = useState<objType[]>([]); // 公司数据
  const [selectCompany, setSelectCompany] = useState<objType>(); // 选择的公司
  const [monitorEquipInfo, setMonitorEquipInfo] = useState<deviceDataType>(deviceDataInt); // 设备详情
  const [selectDevice, setSelectDevice] = useState<deviceDataType>(deviceDataInt); // 选择的设备
  const [headList, setHeadList] = useState<objType[]>([]); // 表头
  const [monitorValues, setMonitorValues] = useState<objType[]>([]); // table数据list
  const [selectTime, setSelectTime] = useState<[moment.Moment, moment.Moment]>([
    moment().subtract(1, 'day'),
    moment(),
  ]);
  const [deviceLoading, setDeviceLoading] = useState<boolean>(false); // 设备详情加载状态
  const map: Map = new Map();
  const layerDom: React.MutableRefObject<TrackingLayer> = useRef();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false); // 点击设备打开modal详情
  const [insertModal, setInsertModal] = useState(false); // 接入数量、在线、掉线
  const [monitorModal, setMonitorModal] = useState(false); // 监测指标
  const [online, setOnline] = useState<null | 0 | 1>(null);
  const [dataNum, setDataNum] = useState<Record<string, number>>({}); // 监测设备数量
  const [alarmNum, setAlarmNum] = useState<Record<string, number>>({}); // 报警数量
  const [alarmModal, setAlarmModal] = useState<boolean>(false); // 报警modal
  const [clickAlarm, setClickAlarm] = useState<objType>({}); // 点击报警类型
  const [selctList, setSelctList] = useState<IconDataTypes['type'][]>([
    'A22A08A07',
    'A22A08A06',

    // 'A22A08A07A01',
    // 'A22A08A07A05',
    // // 'A22A08A01',
    // 'A22A08A06A05',
  ]); // 监测类型
  const [isPark, setIsPark] = useState<boolean>(false); // 是否是园区账号登录
  const [isMapReady, setIsMapReady] = useState<boolean>(false); // 地图是否已准备就绪
  const [devicePoint, setDevicePoint] = useState<DeviceLocationMap>({
    A22A08A07: [],
    A22A08A06: [],
    // A22A08A07A01: [],
    // A22A08A07A05: [],
    // // A22A08A01: [],
    // A22A08A06A05: [],
  }); // 不同类型的设备点位数据

  // 获取监测数量数据
  const queryNumData: () => Promise<void> = async () => {
    const data: {
      code: number;
      data: Record<string, number>;
      msg: string;
    } = await queryEquipPlugInfo({
      companyId: selectCompany?.value === 'all' ? '' : selectCompany?.value,
    });
    if (data.code === 200) {
      setDataNum(data?.data);
    } else {
      setDataNum({});
      message.error('请求数据出错，请刷新重试或联系管理员');
    }
  };
  // 获取报警数量数据
  const queryAlarmNumData: () => Promise<void> = async () => {
    const data: {
      code: number;
      data: Record<string, number>;
      msg: string;
    } = await queryAlarmNumByType({
      companyId: selectCompany?.value === 'all' ? '' : selectCompany?.value,
    });
    if (data.code === 200) {
      setAlarmNum(data?.data);
    } else {
      setAlarmNum({});
      message.error('请求数据出错，请刷新重试或联系管理员');
    }
  };

  // 根据不同类型 存储设备点位数据
  const getDeviceList: (value: IconDataTypes['type'], data: deviceDataType[]) => void = (
    value: IconDataTypes['type'],
    data: deviceDataType[],
  ) => {
    // 使用函数式更新确保状态正确更新
    setDevicePoint((prevDevicePoint) => {
      const newData: DeviceLocationMap = { ...prevDevicePoint };

      switch (value) {
        case 'A22A08A07':
          newData.A22A08A07 = data;
          break;
        case 'A22A08A06':
          newData.A22A08A06 = data;
          break;
        // case 'A22A08A07A01':
        //   newData.A22A08A07A01 = data;
        //   break;
        // case 'A22A08A07A05':
        //   newData.A22A08A07A05 = data;
        //   break;
        // case 'A22A08A01':
        //   newData.A22A08A01 = data;
        //   break;
        // case 'A22A08A06A05':
        //   newData.A22A08A06A05 = data;
        //   break;
        default:
          break;
      }

      return newData;
    });
  };

  // 获取设备点位数据
  const queryDeviceList: (value: IconDataTypes['type']) => Promise<void> = async (
    value: IconDataTypes['type'],
  ) => {
    const data: {
      code: number;
      data: Record<string, string | number>[];
      msg: string;
    } = await queryMonitorEquipList({
      type: value,
      companyId: selectCompany?.value === 'all' ? '' : selectCompany?.value,
    });
    if (data.code === 200) {
      getDeviceList(value, data?.data as deviceDataType[]);
    } else {
      message.error('请求数据出错，请刷新重试或联系管理员');
    }
  };

  // 点击设备查询实时监测数据
  const getItemDetails: (item?: deviceDataType) => Promise<void> = async (
    item?: deviceDataType,
  ) => {
    setDeviceLoading(true);
    try {
      const data: {
        code: number;
        data: {
          headList: objType[];
          monitorEquipInfo: Record<string, string | number>;
          monitorValues: objType[];
        };
        msg: string;
      } = await queryMonitorRealtimeData({
        equipCd: item?.code || selectDevice.code,
        equipId: item?.id || selectDevice.id,
        monitorType: item?.monitorType || selectDevice.monitorType,
        startTm: selectTime[0].format('YYYY-MM-DD HH:mm:ss'),
        endTm: selectTime[1].format('YYYY-MM-DD HH:mm:ss'),
        dateType: '2011',
      });
      if (data?.code === 200) {
        setMonitorEquipInfo(data?.data?.monitorEquipInfo as deviceDataType);
        setHeadList(data?.data?.headList ?? []);
        setMonitorValues(data?.data?.monitorValues ?? []);
      } else {
        setMonitorEquipInfo(deviceDataInt);
        setHeadList([]);
        setMonitorValues([]);
        message.error('请求数据出错，请刷新重试或联系管理员');
      }
    } finally {
      setDeviceLoading(false);
    }
  };

  // 查询当前登陆人
  const queryCurrentUser: () => Promise<void> = async () => {
    const result: {
      company: Record<string, string>;
      user: Record<string, string | number>;
    } = await getCurrentUser();
    oldPartitionList.current = [{ title: result?.company?.unitName ?? '', key: '' }];
  };

  // 查询公司
  const queryCompanyData: (parkAdmin: boolean) => Promise<void> = async (parkAdmin: boolean) => {
    const data: Record<string, string>[] = await queryOrganizationTreeListByLoginUser();
    if (parkAdmin) {
      setCompanyList([{ unitCode: 'all', unitName: '所有公司' }, ...data]);
      setSelectCompany({ value: 'all', label: '所有公司' });
    } else {
      setCompanyList([...data]);
      setSelectCompany({
        value: data ? data[0]?.unitCode : undefined,
        label: data ? data[0]?.unitName : '',
      });
    }
  };

  // 点击设备点位
  const clickItem: (item: deviceDataType) => void = (item: deviceDataType) => {
    setSelectDevice(item);
    getItemDetails(item);
    setIsModalOpen(true);
  };

  // 渲染地图数据
  const drawAreaByDefault: (list: deviceDataType[], img: string) => void = (
    list: deviceDataType[],
    img: string,
  ) => {
    // 检查 layerDom 是否已初始化
    if (!layerDom.current) {
      return;
    }

    // 检查数据是否有效
    if (!list || !Array.isArray(list) || list.length === 0) {
      return;
    }

    list.forEach((item) => {
      // 验证必要的数据字段
      if (!item.id) {
        return;
      }

      const longitude: number = Number(item.longitude);
      const latitude: number = Number(item.latitude);

      try {
        layerDom.current.addMarker({
          layerName: `labelmarker-${item.id}`,
          point: {
            x: longitude,
            y: latitude,
            z: 1,
          },
          img,
          scale: 0.7,
          offset: { x: 0, y: -15 },
          showTips: true,
          attrib: { ...item },
          click: clickItem,
        });
      } catch {
        // 静默处理错误，避免影响其他点位的绘制
      }
    });
  };

  // -------------------------  初始化记载数据  -----------------------

  // **************************************** 地图加载 ****************************************
  // 地图初始化
  const layers: {
    name: string;
    image: string;
    show: boolean;
    list: {
      url: string;
      layer: string;
      style: string;
      format: string;
      tileMatrixSetID: string;
      minimumLevel: number;
      maximumLevel: number;
      tilingScheme: number;
    }[];
  }[] = [
    {
      name: '天地图影像',
      image: tdt,
      show: true,
      list: [
        // 影像
        {
          url: 'http://t0.tianditu.com/img_c/wmts?tk=c8b337de7f212a5178a3b1aa7ec3856b&service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
          layer: 'img',
          style: 'default',
          format: 'image/png',
          tileMatrixSetID: 'c',
          minimumLevel: 1,
          maximumLevel: 17,
          tilingScheme: 2,
        },
        // 影像注记
        {
          url: 'http://t0.tianditu.com/cia_c/wmts?tk=c8b337de7f212a5178a3b1aa7ec3856b&service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
          layer: 'cia',
          style: 'default',
          format: 'image/png',
          tileMatrixSetID: 'c',
          minimumLevel: 1,
          maximumLevel: 17,
          tilingScheme: 2,
        },
      ],
    },
  ];

  const init: (
    center: { x: number; y: number; z: number },
    lensTowards: Record<string, number>,
  ) => void = async (center, lensTowards) => {
    let initPoint: Record<string, number> = dicParams.mapCenterStr;
    // 默认值是安宁的，如果默认位置不对，则园区的经纬度配置有问题
    initPoint = {
      x: center.x ?? 102.34504,
      y: center.y ?? 24.95351,
      z: 10,
      roll: 1,
    };

    map.initMap({
      container: 'mapdemo', // 地图承载容器
      sceneModePicker: true, // 二三维选择按钮-显示控制
      sceneModeChose: 3, // 显示模式 三维: 3 二维: 2  默认值: 3
      positionDisplay: true, // 右下角经纬度显示 默认值: true
      compassDisplay: true, // 罗盘显示 默认值: true
      hostAddr: 'http://***************:8096/check',
      // 地图右键点击事件回调，返回参数(position, pt)
      // rightClickCall: (position, pt) => {
      //   console.log('-----------', position, pt);
      //   // map.getCameraInfo()
      //   // console.log('-----------' , map.getCameraInfo())
      // },
      // 初始位置,地图重置(replace)时用到
      initPlace: {
        point: initPoint,
      },
      layersPro: layers,
      // 地图重置按钮   默认视角, 罗盘中恢复的视角，默认是中国范围
      defaultView: {
        rect: [102.51168768664006, 24.690362748785923, 102.53168768664, 24.700362748785],
        // 数据太大 过不了校验
        // rect: [102.51168768664006, 24.690362748785923, 102.53168768664006, 24.700362748785923],
      },
      // 完成地图加载
      callback: (mapItem: Map) => {
        layerDom.current = new TrackingLayer({ map, viewHeight: 1000 });

        mapItem.flyObject(initPoint);
        mapItem.flyCamera(
          lensTowards,
          // function () {
          //   console.log('镜头定位完成');
          // },
          { duration: 1 },
        );
        mapItem.flyObjectWithCamera(initPoint, { range: 7000, duration: 1 }, () => {
          // 地图完全初始化完成，设置就绪状态
          setIsMapReady(true);
        });
      },
    });
  };

  // 获取当前园区的中心点
  const queryMapCenter: () => Promise<void> = async () => {
    const result: ByParkCodeType = await queryByParkCode();
    init(
      result?.center ? JSON.parse(result.center) : {},
      result?.lensTowards ? JSON.parse(result.lensTowards) : {},
    );
  };

  const columnsData: IYTHColumnProps[] = useMemo(() => {
    const list: IYTHColumnProps[] = [
      {
        title: '监测时间',
        dataIndex: 'TIME',
        key: 'TIME',
        width: 180,
        align: 'center',
        fixed: 'left',
      },
    ];
    headList?.forEach((item) => {
      list.push({
        title: item?.name ?? '-',
        dataIndex: item.code,
        key: item.code,
        width: 180,
        align: 'center',
        children: [
          {
            title: item?.measureUnit ?? '-',
            dataIndex: `${item.code}avg`,
            key: `${item.code}avg`,
            align: 'center',
          },
        ],
      });
    });
    return list;
  }, [headList]);

  useEffect(() => {
    const userInfo: Record<string, string> = CurrentUser();
    // 判断是否是园区账号登录
    if (userInfo.unitType === '-1') {
      setIsPark(true);
      queryCompanyData(true);
    } else {
      queryCompanyData(false);
    }
  }, [CurrentUser]);

  useEffect(() => {
    queryCurrentUser();
    queryMapCenter();
  }, []);

  useEffect(() => {
    if (selectCompany) {
      queryNumData();
      queryAlarmNumData();
      selctList.forEach(async (element) => {
        await queryDeviceList(element);
      });
    }
  }, [selectCompany]);

  useEffect(() => {
    // 确保地图已完全就绪
    if (!isMapReady || !layerDom.current) {
      return;
    }

    // 清除所有现有标记
    layerDom.current.clearAll();

    // 只绘制选中类型的设备点位
    iconShow.forEach((item) => {
      if (selctList.includes(item.type)) {
        const deviceList: deviceDataType[] = devicePoint[item.type];
        drawAreaByDefault(deviceList, item.img);
      }
    });
  }, [isMapReady, devicePoint, selctList]);

  // 中间监测类型点击事件
  function monitoringTypeClick(item: IconDataTypes): void {
    if (selctList.includes(item.type)) {
      getDeviceList(item.type, []);
      setSelctList(selctList.filter((str) => str !== item.type));
    } else {
      queryDeviceList(item.type);
      setSelctList([...selctList, item.type]);
    }
  }

  // 监测设备在线情况
  function equipmentOnlineClick(val: null | 0 | 1): void {
    setInsertModal(true);
    setOnline(val);
  }

  // 报警情况点击
  function alarmClick(item: objType) {
    setClickAlarm(item);
    setAlarmModal(true);
  }

  return (
    <div className={style['map-center']}>
      <div className={style['map-top-conten']}>
        <Select
          showSearch
          placeholder="请选择"
          optionFilterProp="label"
          onSelect={(_: string, option: { value: string; label: string }) => {
            setSelectCompany(option);
            if (layerDom.current) {
              layerDom.current.clearAll();
            }
          }}
          value={selectCompany}
          style={{ width: 200 }}
          options={companyList?.map((item) => {
            return {
              value: item.unitCode,
              label: item.unitName,
            };
          })}
        />
      </div>
      <div id="mapdemo" className={style['map-center']} />
      <div className={style['icon-display']}>
        {iconShow.map((item) => {
          const classStyle: string = selctList.includes(item.type)
            ? style['icon-btn-selected']
            : style['icon-btn'];
          return (
            <div
              className={classStyle}
              key={item.type}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  monitoringTypeClick(item);
                }
              }}
              onClick={() => monitoringTypeClick(item)}
              // onClick={() => {
              //   if (selctList.includes(item.type)) {
              //     getDeviceList(item.type, []);
              //     setSelctList(selctList.filter((str) => str !== item.type));
              //   } else {
              //     queryDeviceList(item.type);
              //     setSelctList([...selctList, item.type]);
              //   }
              // }}
            >
              {item.name}
            </div>
          );
        })}
      </div>
      <div className={style['device-online']}>
        <div className={style.header}>
          <span>监测设备在线情况</span>
        </div>
        <div>
          <div className={style['device-content-echarts']}>
            <GasLeakCharts value={dataNum?.onlinePre} />
          </div>
          <div className={`${style['device-content']} ${style['first-contetn']}`}>
            <div
              className={style['device-item']}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  equipmentOnlineClick(null);
                }
              }}
              onClick={() => equipmentOnlineClick(null)}
              // onClick={() => {
              //   setInsertModal(true);
              //   setOnline(null);
              // }}
            >
              <Image src={proxy + insertNum} preview={false} />
              <span className={style['device-item-num']}>{dataNum?.plugNum || 0}</span>
              <span className={style['device-item-name']}>接入数量</span>
            </div>
            <div
              className={style['device-item']}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  equipmentOnlineClick(1);
                }
              }}
              onClick={() => equipmentOnlineClick(1)}
              // onClick={() => {
              //   setInsertModal(true);
              //   setOnline(1);
              // }}
            >
              <Image src={proxy + onlineNum} preview={false} />
              <span className={style['device-item-num']}>{dataNum?.onlineNum || 0}</span>
              <span className={style['device-item-name']}>在线数量</span>
            </div>
          </div>
          <div className={style['device-content']}>
            <div
              className={style['device-item']}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  setMonitorModal(true);
                }
              }}
              onClick={() => setMonitorModal(true)}
              // onClick={() => setMonitorModal(true)}
            >
              <Image src={proxy + monitorNum} preview={false} />
              <span className={style['device-item-num']}>{dataNum?.monitorNum || 0}</span>
              <span className={style['device-item-name']}>监测指标</span>
            </div>
            <div
              className={style['device-item']}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  equipmentOnlineClick(0);
                }
              }}
              onClick={() => equipmentOnlineClick(0)}
              // onClick={() => {
              //   setInsertModal(true);
              //   setOnline(0);
              // }}
            >
              <Image src={proxy + offlineNum} preview={false} />
              <span className={style['device-item-num']}>{dataNum?.offlineNum || 0}</span>
              <span className={style['device-item-name']}>掉线数量</span>
            </div>
          </div>
        </div>
      </div>
      <div className={style['alarm-situation']}>
        <div className={style.header}>
          <span>报警未消警</span>
        </div>
        <div className={style['alarm-constent']}>
          {alarmData.map((item) => {
            return (
              <div
                className={style['alarm-item']}
                key={item.key}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    alarmClick(item);
                  }
                }}
                onClick={() => alarmClick(item)}

                // onClick={() => {
                //   setClickAlarm(item);
                //   setAlarmModal(true);
                // }}
              >
                <div className={style['alarm-title']}>
                  <span className={style['alarm-num']}>{alarmNum[item.key] || 0}</span>
                  <span className={style['alarm-name']}>{item.name}</span>
                </div>
                <div className={style['alarm-img']}>
                  <Image src={proxy + item.img} preview={false} />
                </div>
              </div>
            );
          })}
        </div>
      </div>
      <Modal
        title="设备监测信息"
        visible={isModalOpen}
        onOk={() => setIsModalOpen(false)}
        onCancel={() => {
          setIsModalOpen(false);
          setSelectDevice(deviceDataInt);
          setSelectTime([moment().subtract(1, 'day'), moment()]);
        }}
        className={style['device-modal']}
        width="70%"
        footer={null}
      >
        <Spin spinning={deviceLoading}>
          <div className={style['modal-device-info']}>
            <Row className={style['modal-device-info-content']}>
              <Col className={style['content-value']} span={6}>
                <span className={style['content-title']}>设备名称：</span>
                <Tooltip title={monitorEquipInfo?.name ?? '-'}>
                  <span>{monitorEquipInfo?.name ?? '-'}</span>
                </Tooltip>
              </Col>
              <Col className={style['content-value']} span={6}>
                <span className={style['content-title']}>设备编号：</span>
                <Tooltip title={monitorEquipInfo?.code ?? '-'}>
                  <span>{monitorEquipInfo?.code ?? '-'}</span>
                </Tooltip>
              </Col>
              <Col className={style['content-value']} span={5}>
                <span className={style['content-title']}>设备运行状态：</span>
                <span>
                  {monitorEquipInfo.online === '1' ? (
                    <span style={{ color: '#3AD50F' }}>在线</span>
                  ) : (
                    <span style={{ color: '#EE8384' }}>掉线</span>
                  )}
                </span>
              </Col>
            </Row>
          </div>

          <div className={style['modal-monitor']}>
            <Row style={{ marginBottom: 10, marginTop: 10 }}>
              <Col span={14}>
                <span>查询实时监测时段：</span>
                <RangePicker
                  showTime={{ format: 'HH:mm:ss' }}
                  defaultValue={[moment().subtract(1, 'day'), moment()]}
                  value={selectTime}
                  format="YYYY-MM-DD HH:mm:ss"
                  onOk={(e) => {
                    if (e[1].diff(e[0], 'days') >= 7) {
                      message.warning('选择时间范围请勿超过7天');
                    }
                    setSelectTime(e);
                  }}
                />
              </Col>
              <Col span={6}>
                <Button type="primary" onClick={() => getItemDetails()}>
                  查询
                </Button>
              </Col>
            </Row>
            <Table
              dataSource={monitorValues}
              className={style['device-table']}
              bordered
              rowClassName={(_, index) => {
                if (index % 2 === 0) {
                  return style['even-row'];
                }
                return style['odd-row'];
              }}
              pagination={{
                showTotal: (total, range) => {
                  const curPage: number = Math.ceil(range[0] / range[1]);
                  const totalPage: number = Math.ceil(total / range[1]);
                  return `共${total}条记录 第${curPage}/${totalPage}页`;
                },
              }}
              columns={columnsData}
              scroll={{ x: headList.length * 180, y: 500 }}
            />
          </div>
        </Spin>
      </Modal>
      {/* 接入数量、在线数量、掉线数量 */}
      <InsertModal
        open={insertModal}
        online={online}
        isPark={isPark}
        selectCompany={selectCompany}
        typeData={alarmData}
        companyList={companyList?.filter((item) => item.unitName !== '所有公司')}
        onClose={() => setInsertModal(false)}
      />
      {/* 监测指标 */}
      <MonotorIndexModal
        isPark={isPark}
        open={monitorModal}
        companyList={companyList?.filter((item) => item.unitName !== '所有公司')}
        selectCompany={selectCompany}
        onClose={() => setMonitorModal(false)}
      />
      {/* 报警情况 */}
      <AlarmModalView
        open={alarmModal}
        isPark={isPark}
        itemData={clickAlarm}
        selectCompany={selectCompany}
        onClose={() => setAlarmModal(false)}
      />
    </div>
  );
};

export default YTHLocalization.withLocal(Partition, {}, YTHLocalization.getLanguage());
