import React, { useEffect, useRef, useState } from 'react';
import { Spin } from 'antd';
import * as echarts from 'echarts/core';
import {
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LegendComponent,
} from 'echarts/components';
import { LineChart } from 'echarts/charts';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import moment from 'moment';
import { ECBasicOption } from 'echarts/types/dist/shared';
import { AirMonitorInfo } from '@/service/analysisApi';

// 注册 ECharts 组件
echarts.use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LineChart,
  CanvasRenderer,
  LegendComponent,
  UniversalTransition,
]);

/**
 * @description 空气质量图表数据类型（与表格数据格式一致）
 */
type AirQualityChartDataType = {
  monitorTime: string; // 监测时间
  so2Num: string; // 二氧化硫
  no2Num: string; // 二氧化氮
  o3Num: string; // 臭氧
  pm25Num: string; // PM2.5
  pm10Num: string; // PM10
  coNum: string; // 一氧化碳
  fluoride: string; // 氟化物
};

/**
 * @description 组件属性类型
 */
type PropsType = {
  /** 加载状态 */
  loading?: boolean;
  /** 空气质量数据（与表格数据格式一致） */
  airQualityData?: AirMonitorInfo[];
};

/**
 * @description 空气质量折线图组件
 * @param props 组件属性
 * @returns React组件
 */
const AirQualityChart: React.FC<PropsType> = (props) => {
  const { loading = false, airQualityData = [] } = props;
  const chartRef: React.MutableRefObject<HTMLDivElement | null> = useRef(null);
  const chartInstance: React.MutableRefObject<echarts.ECharts | null> = useRef(null);
  const [chartData, setChartData] = useState<AirQualityChartDataType[]>([]);

  // 空气质量指标配置（与表格列对应）
  const indicators: { key: string; name: string; unit: string; color: string }[] = [
    { key: 'so2Num', name: '二氧化硫', unit: 'μg/m³', color: '#FF6B6B' },
    { key: 'no2Num', name: '二氧化氮', unit: 'μg/m³', color: '#4ECDC4' },
    { key: 'o3Num', name: '臭氧', unit: 'μg/m³', color: '#45B7D1' },
    { key: 'pm25Num', name: '细微颗粒物PM2.5', unit: 'μg/m³', color: '#96CEB4' },
    { key: 'pm10Num', name: '可吸入颗粒物PM10', unit: 'μg/m³', color: '#FFEAA7' },
    { key: 'coNum', name: '一氧化碳', unit: 'μg/m³', color: '#DDA0DD' },
    { key: 'fluoride', name: '氟化物', unit: 'μg/m³', color: '#FFB347' },
  ];

  // 格式化数值
  const formatValue: (value: number) => number | string = (value: number) => {
    return typeof value === 'number' ? Math.round(value * 100) / 100 || '-' : '-';
  };

  // 图表配置
  const getChartOption: () => ECBasicOption = () => {
    const series: unknown = indicators.map((indicator) => ({
      name: indicator.name,
      type: 'line',
      data: chartData.map((item) =>
        parseFloat(item[indicator.key as keyof AirQualityChartDataType] || '0'),
      ),
      smooth: true,
      symbol: 'circle',
      symbolSize: 4,
      lineStyle: {
        width: 2,
        color: indicator.color,
      },
      itemStyle: {
        color: indicator.color,
      },
    }));

    return {
      tooltip: {
        trigger: 'axis',
        formatter: (params: unknown) => {
          const paramArray: Array<{
            name: string;
            marker: string;
            seriesName: string;
            value: number;
          }> = params as Array<{
            name: string;
            marker: string;
            seriesName: string;
            value: number;
          }>;
          let result: string = `${paramArray[0].name}<br/>`;
          paramArray.forEach((param) => {
            const indicator:
              | { key: string; name: string; unit: string; color: string }
              | undefined = indicators.find((ind) => ind.name === param.seriesName);
            result += `${param.marker} ${param.seriesName}: ${formatValue(param.value)} ${indicator?.unit || ''}<br/>`;
          });
          return result;
        },
      },
      legend: {
        data: indicators.map((ind) => ind.name),
        top: 15,
        left: 'center',
        right: 'auto',
        textStyle: {
          fontSize: 12,
        },
        itemGap: 15,
        padding: [0, 60, 0, 60],
      },
      grid: {
        left: '2%',
        right: '2%',
        bottom: '12%',
        top: '18%',
        containLabel: true,
      },
      toolbox: {
        feature: {
          saveAsImage: {
            show: true,
            title: '保存为图片',
          },
        },
        right: '2%',
        top: '0%',
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: chartData.map((item) => moment(item.monitorTime).format('MM-DD')),
        axisLabel: {
          fontSize: 10,
          rotate: 0,
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        name: 'μg/m³',
        nameTextStyle: {
          fontSize: 12,
        },
        axisLabel: {
          formatter: (value: number) => formatValue(value),
          fontSize: 10,
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#f0f0f0',
          },
        },
      },
      dataZoom: [
        {
          type: 'slider',
          start: 0,
          end: 100,
          height: 15,
          bottom: '3%',
          textStyle: {
            fontSize: 10,
          },
        },
        {
          type: 'inside',
          start: 0,
          end: 100,
        },
      ],
      series,
    };
  };

  // 初始化图表
  useEffect(() => {
    if (chartRef.current && !chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
      // 初始化后立即调用 resize 确保图表占满容器
      setTimeout(() => {
        if (chartInstance.current) {
          chartInstance.current.resize();
        }
      }, 100);
    }
  }, []);

  // 处理传入的真实数据
  const processRealData: (data: AirMonitorInfo[]) => AirQualityChartDataType[] = (
    data: AirMonitorInfo[],
  ): AirQualityChartDataType[] => {
    if (!data || data.length === 0) {
      return [];
    }

    // 处理真实数据，转换为图表需要的格式
    return data.map((item) => ({
      monitorTime: item.monitorTime,
      so2Num: item.so2Num || '0',
      no2Num: item.no2Num || '0',
      o3Num: item.o3Num || '0',
      pm25Num: item.pm25Num || '0',
      pm10Num: item.pm10Num || '0',
      coNum: item.coNum || '0',
      fluoride: item.fluoride || '0',
    }));
  };

  // 当数据变化时更新图表
  useEffect(() => {
    if (airQualityData && airQualityData.length > 0) {
      // 使用真实数据
      const processedData: AirQualityChartDataType[] = processRealData(airQualityData);
      setChartData(processedData);
    } else {
      // 没有数据时清空图表
      setChartData([]);
    }
  }, [airQualityData]);

  // 更新图表数据
  useEffect(() => {
    if (chartInstance.current && chartData.length > 0) {
      const option: ECBasicOption = getChartOption();
      chartInstance.current.setOption(option, true);
      // 设置配置后调用 resize 确保图表正确显示
      setTimeout(() => {
        if (chartInstance.current) {
          chartInstance.current.resize();
        }
      }, 50);
    }
  }, [chartData]);

  // 处理窗口大小变化
  useEffect(() => {
    const handleResize: () => void = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 清理图表实例
  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, []);

  return (
    <Spin spinning={loading}>
      <div
        ref={chartRef}
        style={{
          width: '100%',
          height: '100%',
          minHeight: '280px',
          maxHeight: '300px',
        }}
      />
    </Spin>
  );
};

export default AirQualityChart;
