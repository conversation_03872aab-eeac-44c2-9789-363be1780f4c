import React, { Key, useEffect, useState } from 'react';
import {
  Button,
  Popover,
  Empty,
  Checkbox,
  Table,
  Form,
  Select,
  DatePicker,
  Row,
  Col,
  Divider,
  message,
} from 'antd';
import moment from 'moment';
import type { IYTHColumnProps } from 'yth-ui/es/components/list/index';
import formApi from '@/service/formApi';
import type { ResponseType, ResponsPageType } from '@/service/envApi';
import {
  queryMonitorValueDayReport,
  queryEquipInfoByCompanyId,
  queryMonitorIndex,
  exportMonitorData,
} from '@/service/envApi';
import { monitorTypeConfigs } from './constants';
import { DateListTypes, MonitorTypeConfig } from './types';
import style from './index.module.less';

type objType = Record<string, string>;

type PropsType = {
  /** 监测类型 */
  monitorKey: string;
  /** 父级传入的查询参数 */
  queryData: Record<string, string>;
  /** 在线监测类型 */
  onLineMonitorType: string;
  /** 所属单位数据 */
  companyList: Record<string, string>[];
  /** 采集频率字典数据 */
  monitorFrequencyList: DateListTypes[];
  /** 监测类型配置-环境质量监测，污染源监测，公用工程监测 */
  monitorConfigType: string;
};

/**
 * @description 报表类型types
 */
type ReportTypes = {
  name: '日报表' | '月报表';
  code: 'date' | 'month';
  key: string;
};

/**
 * table 最后一页增加以下行数据
 */
const lastCol: Record<string, string> = {
  cou: '个数',
  min: '最小值',
  max: '最大值',
  mean: '均值',
  total: '合计',
};

/**
 * 均值和累计值数据标识
 */
const dataformat: { value: string; key: string }[] = [
  { value: '均值', key: 'avg' },
  { value: '累计值', key: 'sum' },
];

/**
 * 报表类型
 */
const reportTypeData: ReportTypes[] = [
  { name: '日报表', code: 'date', key: '1' },
  { name: '月报表', code: 'month', key: '2' },
];

/**
 * @description 导出modal
 * @param param0
 * @returns
 */
const exportReportModl: React.FC<PropsType> = (props) => {
  const {
    monitorKey,
    onLineMonitorType,
    queryData,
    companyList,
    monitorConfigType,
    monitorFrequencyList,
  } = props;
  const [form] = Form.useForm();
  const config: MonitorTypeConfig = monitorTypeConfigs[monitorConfigType];
  const [checkValue, setCheckValue] = useState<React.Key[]>([]); // 因子配置选择的可见表头项
  const [columnList, setColumnList] = useState<IYTHColumnProps[]>([]); // 原始column表头数据
  const [filterCol, setFilterCol] = useState<IYTHColumnProps[]>([]); // 根据因子配置筛选的column数据
  const [resData, setResData] = useState<objType[]>([]); // 列表返回数据
  const [loading, setLoading] = useState<boolean>(false);
  const [reportType, setReportType] = useState<ReportTypes['code']>('date'); // 报表类型
  const [showEnd, setShowEnd] = useState<boolean>(false); // 根据条件判断是否展示结束时间
  const [deviceDataList, setDeviceDataList] = useState<objType[]>([]); // 设备数据list
  const [monitorList, setMonitorList] = useState<objType[]>([]); // 监测类型数据
  const [dateTypeKey, setDateTypeKey] = useState<string>(''); // 时间类型
  const [selectedEquipId, setSelectedEquipId] = useState<string>(''); // 当前选中的设备ID

  // 获取监测类型数据
  const getMonitorList: () => Promise<void> = async () => {
    // 获取监测类型字典数据
    const { list } = await formApi.getDictionary({
      condition: {
        // 根据配置决定使用哪个父级字典码
        fatherCode: config.filterMonitorList ? 'A22A08A07' : monitorKey,
      },
      currentPage: 0,
      pageSize: 0,
    });

    let finalList: objType[] = list;
    // 如果需要过滤监测类型列表
    if (config.filterMonitorList && config.filterCondition) {
      finalList = list?.filter(config.filterCondition) || [];
    }
    setMonitorList([...finalList]);
  };

  const fixedCol: IYTHColumnProps[] = [
    {
      code: 'collectTime',
      name: '监测时间',
      width: 180,
      title: '监测时间',
      align: 'center',
      dataIndex: 'collectTime',
      sorter: true,
      render: (_r, record) => {
        if (record.stat) {
          return lastCol[record.stat];
        }
        return record.TIME || '-';
      },
      fixed: 'left',
      key: 'collectTime',
    },
  ];

  /**
   * @description 格式化数据
   * @param treeData
   * @returns
   */
  const dealTreeData: (list: objType[]) => IYTHColumnProps[] = (treeData: objType[]) => {
    const data: IYTHColumnProps[] = treeData.map((item, index) => {
      const newItem: IYTHColumnProps = {
        ...item,
        key: item.code + index,
        dataIndex: item.code + index,
        title: item.name,
        width: 180,
        align: 'center',
      };
      newItem.children = dataformat.map((str) => {
        return {
          key: item.code + str.key + index,
          dataIndex: item.code + str.key + index,
          align: 'center',
          title: str.value,
          children: [
            {
              key: item.code + str.key,
              dataIndex: item.code + str.key,
              align: 'center',
              title: item.measureUnit,
              render: (_r: number, record: objType) => {
                const newVal: string = item.code + str.key;
                if (record[newVal]) {
                  // if (record.stat) {
                  //   return record[newVal] ? Math.trunc(record[newVal] * 1000) / 1000 : '-';
                  // }
                  // if (
                  //   item.firstLevelMax &&
                  //   item.firstLevelMin &&
                  //   (record[newVal] > item.firstLevelMax || record[newVal] < item.firstLevelMin)
                  // ) {
                  //   return (
                  //     <div className={style.triangle}>
                  //       {record[newVal] ? Math.trunc(record[newVal] * 1000) / 1000 : '-'}
                  //     </div>
                  //   );
                  // }
                  // return record[newVal] ? Math.trunc(record[newVal] * 1000) / 1000 : '-';
                  return record[newVal];
                }
                return '-';
              },
            },
          ],
        };
      });
      if (!item.isTotalizeValue || item.isTotalizeValue === '0') {
        delete newItem.children[1];
      }

      return newItem;
    });
    return data;
  };

  /**
   * @description 因子配置切换
   * @param list
   */
  const checkValueChange: (list: string[]) => void = (list: string[]) => {
    const newColList: IYTHColumnProps[] = [];
    list?.forEach((item) => {
      columnList.forEach((str) => {
        if (item === str.code) {
          newColList.push({ ...str });
        }
      });
    });
    setFilterCol([...fixedCol, ...newColList]);
  };

  /**
   * @description 简单格式化时间
   * @param dataType // 报表类型
   * @param dataSource  // 数据源
   * @param startTime
   * @param endTime
   * @returns
   */
  const changeTimeFormat: (
    dataType: ReportTypes['code'],
    dataSource: string,
    startTime: moment.Moment,
    endTime: moment.Moment,
  ) => {
    newStart: string;
    newEnd: string;
  } = (dataType: ReportTypes['code'], dataSource: string, startTime, endTime) => {
    let newStart: string = '';
    let newEnd: string = '';
    // 查找数据源对应的配置
    const dataSourceConfig: DateListTypes | undefined = monitorFrequencyList.find(
      (item) => item.key === dataSource,
    );

    // 处理月报表类型
    if (dataType === 'month') {
      newStart = moment(startTime).startOf('month').format('YYYY-MM-DD HH:mm:ss');
      newEnd = moment(startTime).endOf('month').format('YYYY-MM-DD HH:mm:ss');
    }
    // 查找数据源对应的配置，处理年份类型 (dataCode: '9999')
    else if (dataSourceConfig?.dataCode === '9999') {
      newStart = moment(startTime).startOf('year').format('YYYY-MM-DD HH:mm:ss');
      newEnd = moment(startTime).endOf('year').format('YYYY-MM-DD HH:mm:ss');
    }
    // 处理其他类型
    else {
      switch (dataSourceConfig?.dataCode) {
        case '2031': // 日均数据
          newStart = moment(startTime).startOf('day').format('YYYY-MM-DD HH:mm:ss');
          newEnd = moment(endTime).endOf('day').format('YYYY-MM-DD HH:mm:ss');
          break;
        case '2011': // 实时数据 - 保持用户选择的具体时间（到秒）
          newStart = moment(startTime).format('YYYY-MM-DD HH:mm:ss');
          newEnd = moment(endTime).format('YYYY-MM-DD HH:mm:ss');
          break;
        case '2061': // 时均数据 - 保持用户选择的具体时间（到小时）
          newStart = moment(startTime).format('YYYY-MM-DD HH:mm:ss');
          newEnd = moment(endTime).format('YYYY-MM-DD HH:mm:ss');
          break;
        default:
          newStart = moment(startTime).format('YYYY-MM-DD HH:mm:ss');
          newEnd = moment(endTime).format('YYYY-MM-DD HH:mm:ss');
          break;
      }
    }

    return { newStart, newEnd };
  };

  /**
   * 获取当前应该使用的日期选择器类型
   */
  const getPickerType: () => 'date' | 'month' | 'year' = () => {
    if (reportType === 'month') {
      return 'month';
    }
    if (monitorFrequencyList.find((item) => item.key === dateTypeKey)?.pickerType === 'year') {
      return 'year';
    }
    return 'date';
  };

  /**
   * 获取当前应该使用的日期格式
   */
  const getDateFormat: () => string = () => {
    const currentConfig: DateListTypes | undefined = monitorFrequencyList.find(
      (item) => item.key === dateTypeKey,
    );
    if (currentConfig?.format) {
      return currentConfig.format;
    }
    // 默认格式
    if (dateTypeKey === '2011') {
      return 'YYYY-MM-DD HH:mm:ss'; // 实时数据：到秒
    }
    if (dateTypeKey === '2061') {
      return 'YYYY-MM-DD HH'; // 时均数据：到小时
    }
    if (dateTypeKey === '2031') {
      return 'YYYY-MM-DD'; // 日均数据：到日
    }
    if (dateTypeKey === '9999') {
      return 'YYYY'; // 年均数据：到年
    }
    return 'YYYY-MM-DD';
  };

  /**
   * 获取时间选择器的配置
   */
  const getShowTimeConfig: () =>
    | false
    | { format?: string; hideDisabledOptions?: boolean; defaultValue?: moment.Moment } = () => {
    if (dateTypeKey === '2011') {
      // 实时数据：显示时分秒
      return { format: 'HH:mm:ss' };
    }
    if (dateTypeKey === '2061') {
      // 时均数据：只显示小时，分钟秒钟设为00
      return {
        format: 'HH',
        hideDisabledOptions: true,
        defaultValue: moment().startOf('hour'),
      };
    }
    return false;
  };

  /**
   * 检查时间范围是否超过限制
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @param dataSourceType 数据源类型
   * @returns 是否超过限制
   */
  const checkDateRangeLimit: (
    startTime: moment.Moment,
    endTime: moment.Moment,
    dataSourceType: string,
  ) => boolean = (
    startTime: moment.Moment,
    endTime: moment.Moment,
    dataSourceType: string,
  ): boolean => {
    if (!startTime || !endTime) return false;

    const diffDays: number = endTime.diff(startTime, 'days');

    // 实时数据和时均数据最大只能选择7天
    if (dataSourceType === '2011' || dataSourceType === '2061') {
      if (diffDays > 7) {
        message.warning('实时数据和时均数据最大只能选择7天范围');
        return true;
      }
    }

    // 日均数据最大只能选择30天
    if (dataSourceType === '2031') {
      if (diffDays > 30) {
        message.warning('日均数据最大只能选择30天范围');
        return true;
      }
    }

    return false;
  };

  /**
   * @description 获取列表数据
   * @param params 查询参数
   * @param id 设备ID
   */
  const getDataList: (id?: string) => Promise<void> = async (equipId?: string) => {
    try {
      const { startDate, endDate, deviceCode, formCompany } = form.getFieldsValue();
      const newSourceType: string = form.getFieldValue('dataSourceType');
      const queryParams: {
        dataType: ReportTypes['code'];
        dataSource: string;
        startTime: moment.Moment;
        endTime: moment.Moment;
      } = {
        dataType: reportType,
        dataSource: form.getFieldValue('dataSourceType') ?? '',
        startTime: startDate,
        endTime: endDate,
      };

      // 获取设备ID
      const newequipId: string =
        deviceDataList.find((str) => str.code === deviceCode)?.id || equipId;

      // 检查时间范围限制
      if (checkDateRangeLimit(queryParams.startTime, queryParams.endTime, queryParams.dataSource)) {
        return; // 超过限制，不执行查询
      }

      // 格式化时间
      const { newStart, newEnd } = changeTimeFormat(
        queryParams.dataType,
        queryParams.dataSource,
        queryParams.startTime,
        queryParams.endTime,
      );

      setLoading(true);

      // 调用API获取数据
      const resDatas: ResponseType = await queryMonitorValueDayReport({
        monitorType: onLineMonitorType,
        equipCd: deviceCode,
        equipId: newequipId,
        orgCd: formCompany,
        startTm: newStart,
        endTm: newEnd,
        dateType: reportType === 'month' ? '4' : newSourceType,
      });

      // 处理返回结果
      if (resDatas.code === 200 && resDatas.data) {
        setResData(Array.isArray(resDatas.data) ? [...resDatas.data] : []);
      } else {
        setResData([]);
      }
    } catch {
      setResData([]);
    } finally {
      setLoading(false);
    }
  };

  /**
   * @description 获取表头列数据并更改结构
   * 使用当前选中的设备ID和监测频率获取监测指标
   */
  const getColData: () => Promise<void> = async () => {
    // 检查必要参数：设备ID和监测频率都必须存在
    if (!selectedEquipId || !dateTypeKey) {
      // 缺少必要参数时，只显示时间列
      setFilterCol([...fixedCol]);
      return;
    }

    // 开始加载
    setLoading(true);
    // 清空现有数据
    setColumnList([]);
    setCheckValue([]);
    // 调用API获取监测指标
    const data: ResponsPageType = await queryMonitorIndex(selectedEquipId, dateTypeKey);
    if (data.code === 200 && data.data.length > 0) {
      const newList: string[] = (data?.data.map((item) => item.code) || []) as string[];
      const newDatas: IYTHColumnProps[] = data.data as IYTHColumnProps[];
      setColumnList([...newDatas]);
      setFilterCol([...fixedCol, ...dealTreeData(newDatas)]);
      setCheckValue([...newList]);
      // 获取监测数据
      getDataList(selectedEquipId);
    } else {
      // 如果没有监测指标，只显示时间列
      setFilterCol([...fixedCol]);
    }
    // 结束加载
    setLoading(false);
  };

  /**
   * @description 根据选择公司获取设备
   */
  const getDeviceData: (reset?: boolean) => Promise<void> = async (reset = true) => {
    const { monitorType: newMonitorType, formCompany } = form.getFieldsValue();
    setLoading(true);
    setColumnList([]);
    setCheckValue([]);
    setFilterCol([...fixedCol]);
    const data: ResponseType = await queryEquipInfoByCompanyId({
      companyId: formCompany,
      type: monitorKey,
      monitorType: newMonitorType,
      monitorOnline: '1',
    });
    if (data.code === 200 && Array.isArray(data.data) && data.data.length > 0) {
      // 如果reset为true，则设置默认设备
      if (reset) {
        form.setFieldsValue({ deviceCode: data.data[0]?.code });
        // 设置选中的设备ID（这会触发useEffect自动调用getColData）
        setSelectedEquipId(data.data[0]?.id || '');
      }
      setDeviceDataList([...data.data]);
    } else {
      form.resetFields(['deviceCode']);
      setDeviceDataList([]);
      setSelectedEquipId('');
      setColumnList([]);
      setCheckValue([]);
    }
    setLoading(false);
  };

  // 文件导出
  const uploadTemplate: () => Promise<void> = async () => {
    const { startDate, endDate, dataSourceType, formCompany, deviceCode } = form.getFieldsValue();

    // 检查时间范围限制
    if (checkDateRangeLimit(startDate, endDate, dataSourceType)) {
      return; // 超过限制，不执行导出
    }

    setLoading(true);
    const equipData: objType = deviceDataList?.find((device) => device.code === deviceCode) || {};
    const { newStart, newEnd } = changeTimeFormat(reportType, dataSourceType, startDate, endDate);
    const newList: IYTHColumnProps[] = [...filterCol];
    const companyName: string =
      companyList.find((str) => str.companyId === formCompany)?.supplyUnit || '-';
    newList.shift();
    const params: {
      companyName: string;
      equipName: string;
      equipCd: string;
      equipId: string;
      orgCd: string;
      startTm: string;
      endTm: string;
      dateType: string;
      monitorType: string;
      reportType: string;
      monitorIndexHeaderParamList: IYTHColumnProps[];
      frequency: string;
    } = {
      companyName,
      equipName: equipData.name ?? '',
      equipCd: equipData.code ?? '',
      equipId: equipData.id ?? '',
      orgCd: formCompany || '',
      startTm: newStart,
      endTm: newEnd,
      dateType: reportType === 'month' ? '4' : dataSourceType,
      monitorType: onLineMonitorType,
      reportType: reportTypeData.find((r) => r.code === reportType).key,
      monitorIndexHeaderParamList: newList,
      frequency: dateTypeKey,
    };
    const responseData: BlobPart = await exportMonitorData({
      data: params,
      responseType: 'formData',
    });
    setLoading(false);
    const href: string = window.URL.createObjectURL(
      new Blob([responseData], { type: 'application/vnd.ms-excel;charset=utf-8' }),
    );
    const link: HTMLAnchorElement = document.createElement('a');
    link.href = href;
    link.download = `${companyName + equipData.name}${config.exportName}报表`;
    window.document.body.appendChild(link);
    link.click();
    window.document.body.removeChild(link);
    window.URL.revokeObjectURL(href);
  };

  /**
   * @description Popover弹出内容content 因子配置
   */
  const popoverPontent: React.JSX.Element = (
    <div
      style={{
        width: 150,
        overflowY: 'auto',
      }}
    >
      {columnList.length >= 1 ? (
        <Checkbox.Group
          value={checkValue}
          // options={columnList.map((item) => ({ label: item.name, value: item.code }))}
          onChange={(e) => {
            if (e.length === 0) return;
            setCheckValue(e as string[]);
            checkValueChange(e as string[]);
          }}
        >
          <Row>
            {columnList.map((item) => {
              return (
                <Col key={item.code} span={24}>
                  <Checkbox value={item.code}>{item.name}</Checkbox>
                </Col>
              );
            })}
          </Row>
        </Checkbox.Group>
      ) : (
        <Empty />
      )}
    </div>
  );

  useEffect(() => {
    // 根据父级传入的表单数据 设置表单数据
    form.setFieldsValue({
      formCompany: queryData?.formCompany,
      dataSourceType: queryData?.formDateType,
      deviceCode: queryData?.deviceCode,
      monitorType: queryData?.monitorType,
      // 如果formDateType为9999 即年数据，则只有一个时间
      startDate:
        queryData?.formDateType === '9999' ? queryData?.rangeDate : queryData?.rangeDate?.[0],
      endDate: queryData?.formDateType === '9999' ? undefined : queryData?.rangeDate?.[1],
    });
    // 实时数据、时均数据、日均数据都需要展示范围选择器，只有年均数据不需要
    if (queryData?.formDateType === '9999') {
      setShowEnd(false);
    } else {
      setShowEnd(true);
    }
    setDateTypeKey(queryData?.formDateType);
    getDeviceData(false);
    setReportType('date');
    setSelectedEquipId(queryData?.selectedEquipId);
    setDateTypeKey(queryData?.formDateType);
  }, [queryData]);

  useEffect(() => {
    // 创建Set去重
    const uniqueCodes: Set<Key> = new Set(checkValue);
    // 根据选中的因子筛选列
    const newList: IYTHColumnProps[] = [];
    // 使用Set.has方法检查code是否存在
    columnList.forEach((item) => {
      if (uniqueCodes.has(item.code)) {
        newList.push(item);
      }
    });
    setFilterCol([...fixedCol, ...dealTreeData(newList)]);
  }, [JSON.stringify(checkValue)]);

  useEffect(() => {
    if (monitorKey !== 'A22A08A01') {
      getMonitorList();
    }
  }, [monitorKey]);

  // 监听设备ID和采集频率变化，重新获取列数据
  useEffect(() => {
    if (selectedEquipId && dateTypeKey) {
      getColData();
    }
  }, [selectedEquipId, dateTypeKey]);

  const disabledStartDate: (current: moment.Moment) => boolean = (current) => {
    const { endDate } = form.getFieldsValue();
    return current && current > endDate;
  };

  const disabledEndDate: (current: moment.Moment) => boolean = (current) => {
    const { startDate } = form.getFieldsValue();
    return current && current < startDate;
  };

  return (
    <div style={{ width: '100%', height: '100%' }} className={style['gas-export-modal']}>
      <Form
        form={form}
        name="exportReportForm"
        className={style['ant-gasleak-form']}
        onFinish={() => getDataList()}
        initialValues={{ formReport: reportType }}
      >
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item name="formCompany" label="所属单位">
              <Select
                placeholder="请选择"
                showSearch
                style={{ fontSize: 12 }}
                onSearch={() => {
                  getDeviceData();
                }}
                optionFilterProp="children"
                filterOption={(input: string, option?: { children: string; value: string }) =>
                  (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
                }
                onChange={() => {
                  getDeviceData();
                }}
              >
                {(companyList || []).map((item) => (
                  <Select.Option key={item.companyId}>{item.supplyUnit || '-'}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          {monitorKey !== 'A22A08A01' && (
            <Col span={6}>
              <Form.Item name="monitorType" label="监测类型">
                <Select
                  placeholder="请选择"
                  showSearch
                  style={{ fontSize: 12 }}
                  onChange={() => {
                    // 监测类型变化时，先清空设备ID以确保重新查询
                    setSelectedEquipId('');
                    form.resetFields(['deviceCode']);
                    getDeviceData();
                  }}
                >
                  {(monitorList || []).map((item) => (
                    <Select.Option key={item.code}>{item.text}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          )}
          <Col span={6}>
            <Form.Item name="deviceCode" label="设备名称">
              <Select
                placeholder="请选择"
                showSearch
                style={{ fontSize: 12 }}
                optionFilterProp="children"
                filterOption={(input: string, option?: { children: string; value: string }) =>
                  (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
                }
                onChange={(deviceCode) => {
                  // 更新选中的设备ID
                  const equipId: string =
                    deviceDataList.find((item) => item.code === deviceCode)?.id || '';
                  setSelectedEquipId(equipId);
                }}
              >
                {(deviceDataList || []).map((item) => (
                  <Select.Option key={item.code}>{item.name}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          {/* <Col span={6}>
            <Form.Item name="formReport" label="类型">
              <Select
                placeholder="请选择"
                onChange={(value) => {
                  setReportType(value);
                }}
              >
                {reportTypeData.map((item) => (
                  <Select.Option key={item.code}>{item.name}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col> */}
          {reportType === 'date' && (
            <Col span={6}>
              <Form.Item name="dataSourceType" label="采集频率">
                <Select
                  placeholder="请选择"
                  onSelect={(e: string, option: { key: string }) => {
                    // 更新数据源类型状态
                    setDateTypeKey(e);
                    // 实时数据、时均数据、日均数据都需要展示范围选择器，只有年均数据不需要
                    if (option.key === '9999') {
                      setShowEnd(false);
                    } else {
                      setShowEnd(true);
                    }
                  }}
                >
                  {monitorFrequencyList.map((item) => (
                    <Select.Option key={item.key}>{item.text}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          )}
          <Col span={10} style={{ display: 'flex' }}>
            <Form.Item name="startDate" label="监测时间">
              <DatePicker
                style={{ width: '100%' }}
                picker={getPickerType()}
                showTime={getShowTimeConfig()}
                format={getDateFormat()}
                size="large"
                disabledDate={disabledStartDate}
              />
            </Form.Item>
            {reportType === 'date' && showEnd && (
              <Form.Item name="endDate" label="至">
                <DatePicker
                  style={{ width: '100%' }}
                  picker={getPickerType()}
                  showTime={getShowTimeConfig()}
                  format={getDateFormat()}
                  disabledDate={disabledEndDate}
                  size="large"
                />
              </Form.Item>
            )}
          </Col>
          <Col span={14} style={{ textAlign: 'end' }}>
            <Button className={style['top-button']} type="primary" htmlType="submit">
              查询
            </Button>
            <Button
              style={{ marginLeft: 15 }}
              className={style['top-button']}
              type="primary"
              onClick={uploadTemplate}
            >
              导出Excel
            </Button>
          </Col>
        </Row>
      </Form>
      <Divider style={{ margin: 0 }} dashed />
      <div className={style['export-popover']}>
        <Popover
          placement="bottomRight"
          overlayClassName={style['online-export-popover-content']}
          trigger="click"
          title={null}
          content={popoverPontent}
        >
          <Button type="primary" size="small" ghost>
            因子配置
          </Button>
        </Popover>
      </div>
      <Table
        loading={loading}
        bordered
        rowKey={(row) => row.id}
        dataSource={resData}
        className={style['export-table']}
        rowClassName={(_, index) => {
          if (index % 2 === 0) {
            return style['even-row'];
          }
          return style['odd-row'];
        }}
        columns={filterCol}
        scroll={{ x: checkValue.length * 180, y: 500 }}
      />
    </div>
  );
};
export default exportReportModl;
